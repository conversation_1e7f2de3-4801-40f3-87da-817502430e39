from odoo import models, fields, api

class ProjectEditWizard(models.TransientModel):
    _name = 'project.edit.wizard'
    _description = 'Edit Project Wizard'

    project_id = fields.Many2one('project.project', string="Project", required=True)
    name = fields.Char(string="Project Name", required=True)
    project_poc = fields.Many2one('hr.employee', string="Project POC")
    date_due = fields.Date(string="Due Date")
    budget = fields.Monetary(string="Budget", currency_field='currency_id')
    total_spent = fields.Monetary(string="Total Spent", currency_field='currency_id', tracking=True)
    currency_id = fields.Many2one('res.currency', string='Currency')

    @api.model
    def default_get(self, fields_list):
        res = super().default_get(fields_list)
        project = self.env['project.project'].browse(self.env.context.get('active_id'))
        if project:
            res.update({
                'project_id': project.id,
                'name': project.name,
                'project_poc': project.project_poc.id,
                'date_due': project.date_due,
                'budget': project.budget,
                'total_spent': project.total_spent,
                'currency_id': project.currency_id.id,
            })
        return res

    def action_save(self):
        self.project_id.write({
            'name': self.name,
            'project_poc': self.project_poc.id,
            'date_due': self.date_due,
            'budget': self.budget,
            'total_spent': self.total_spent,
        })
        return {'type': 'ir.actions.act_window_close'}