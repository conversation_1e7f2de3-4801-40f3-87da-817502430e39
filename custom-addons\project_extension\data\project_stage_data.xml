<odoo>
    <data noupdate="0"> 
        <record id="stage_awaiting_requirement" model="project.project.stage">
            <field name="name">Awaiting Requirement</field>
            <field name="sequence">1</field>
        </record>
        <record id="stage_pre_sales" model="project.project.stage">
            <field name="name">Pre Sales</field>
            <field name="sequence">2</field>
        </record>
        <record id="stage_quotation_approved" model="project.project.stage">
            <field name="name">Quotation Approved</field>
            <field name="sequence">3</field>
        </record>
        <record id="stage_quotation_sent" model="project.project.stage">
            <field name="name">Quotation Sent</field>
            <field name="sequence">4</field>
        </record>
        <record id="stage_active" model="project.project.stage">
            <field name="name">Active</field>
            <field name="sequence">5</field>
        </record>
        <record id="stage_dispatch" model="project.project.stage">
            <field name="name">Dispatch</field>
            <field name="sequence">6</field>
        </record>
        <record id="stage_completed" model="project.project.stage">
            <field name="name">Completed</field>
            <field name="sequence">7</field>
        </record>
        <record id="stage_closed" model="project.project.stage">
            <field name="name">Closed</field>
            <field name="sequence">8</field>
        </record>
    </data>    
</odoo>