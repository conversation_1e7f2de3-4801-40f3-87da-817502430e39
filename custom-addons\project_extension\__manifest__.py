{
    'name': 'Custom Project',
    'version': '1.0.4',
    'author': 'T-Works',
    'category': 'Project',
    'summary': 'Custom Project Module',
    'description': """
        This is a custom project module which has the following functionality:
        - Creating a project
        - Viewing all projects in the active stage
        - Assigning a project to a particular person and team of persons
    """,

    'depends': ['project','job_card_management'],
    'data': [
        'security/project_security.xml',
        'security/project_additional_groups.xml',
        'security/ir.model.access.csv',
        'views/project_form.xml',
        'views/project_view.xml',
        'data/project_sequence.xml',
        'data/project_stage_data.xml',
        'wizard/project_edit_view.xml',
        'wizard/project_complete_view.xml',
        'wizard/project_edit_wizard_action.xml',
        'wizard/project_presales_edit_wizard_view.xml',
        'wizard/project_pr_document_wizard_view.xml',
    ],
    
    'installable': True,  
    'application': True,  
    'auto_install': False,
    'post_init_hook': 'remove_default_project_stages',
}
