# Bidirectional Enquiry-Project Stage Synchronization

## Overview
This feature provides intelligent bidirectional synchronization between enquiry and project stages, with different sync directions based on the project phase. The sync logic ensures that the appropriate system (enquiry or project) drives stage changes at the right time in the project lifecycle.

## Stage Mapping

| Enquiry Stage | Project Stage | Notes |
|---------------|---------------|-------|
| Onboarding | N/A | No project created yet |
| Awaiting Requirements | Awaiting Requirement | Project created at this stage |
| **Pre-Sales** | **Pre Sales** | **Main sync scenario** |
| Quotation Approved | Quotation Approved | Exact match |
| Quotation Sent | Quotation Sent | Exact match |
| Active | Active | Exact match |
| Dispatch | Dispatch | Exact match |
| Completed | Completed | Exact match |
| Inactive/Closed | Closed | Name mapping |

## Sync Direction Logic

### Phase 1: Enquiry-Driven (Until Active Stage)
**Stages**: Awaiting Requirements → Pre-Sales → Quotation Approved → Quotation Sent → Active
- **Direction**: Enquiry → Project
- **Logic**: Business development and pre-sales activities are managed through enquiry workflow
- **Result**: Project stage automatically follows enquiry progression

### Phase 2: Project-Driven (After Active Stage)
**Stages**: Dispatch → Completed → Closed
- **Direction**: Project → Enquiry
- **Logic**: Project execution and delivery are managed through project workflow
- **Result**: Enquiry stage automatically follows project progression

### Special Case: Enquiry Closure
**Trigger**: Enquiry moved to "Inactive/Closed" at any stage
- **Direction**: Enquiry → Project (always)
- **Logic**: Business decision to close enquiry should immediately close project
- **Result**: Project immediately moves to "Closed" stage

## Workflow Scenarios

### Scenario 1: Normal Project Progression
1. **Enquiry**: Onboarding → Awaiting Requirements (Project created)
2. **Enquiry**: Awaiting Requirements → Pre-Sales → Project follows
3. **Enquiry**: Pre-Sales → Quotation Approved → Project follows
4. **Enquiry**: Quotation Approved → Active → Project follows
5. **Project**: Active → Dispatch → Enquiry follows
6. **Project**: Dispatch → Completed → Enquiry follows

### Scenario 2: Early Closure
- **Any Stage**: Enquiry → Inactive/Closed → Project → Closed

## Technical Implementation

### Enquiry Model: `_sync_project_stage_and_create()`
```python
def _sync_project_stage_and_create(self, vals):
    # 1. Check context flag to avoid infinite loops
    # 2. Define enquiry-driven stages (until Active)
    # 3. Handle special case for Inactive/Closed
    # 4. Update project stage and create history
```

### Project Model: `_sync_enquiry_stage_from_project()`
```python
def _sync_enquiry_stage_from_project(self, project):
    # 1. Check context flag to avoid infinite loops
    # 2. Define project-driven stages (after Active)
    # 3. Map project stages to enquiry stages
    # 4. Update enquiry stage with context flag
```

### Loop Prevention
- **Context Flag**: `syncing_stages`
- **Purpose**: Prevents infinite loops between enquiry and project sync
- **Implementation**: Each sync operation sets context flag, receiving model checks and skips if set

### Stage History Tracking
- **Model**: `project.stage.history`
- **Fields**: project_id, stage_id, entered_date
- **Purpose**: Complete audit trail of project progression
- **Created**: For both enquiry→project and project→enquiry sync operations

## Benefits

✅ **Intelligent Sync Direction**: Appropriate system drives stages based on project phase
✅ **Automatic Synchronization**: No manual stage updates needed in either direction
✅ **Data Consistency**: Enquiry and project stages always reflect current status
✅ **Complete Audit Trail**: History maintained for all sync operations
✅ **Loop Prevention**: Robust mechanism prevents infinite sync loops
✅ **Special Case Handling**: Enquiry closure immediately closes projects
✅ **Error Handling**: Graceful handling of missing stages/projects
✅ **Selective Application**: Only applies to project-generating enquiry types

## Testing Scenarios

1. **Enquiry-Driven Sync**: Test stages until Active (enquiry controls project)
2. **Project-Driven Sync**: Test stages after Active (project controls enquiry)
3. **Enquiry Closure**: Test Inactive/Closed at different stages (always closes project)
4. **Loop Prevention**: Verify no infinite loops during bidirectional sync
5. **Multiple Enquiry Types**: Design & Engineering, Fabrication/Prototyping
6. **Non-Project Enquiries**: Startup Support (no sync expected)
7. **Stage History**: Verify complete history for both sync directions

## Error Handling

- **Missing Project Stage**: Sync skipped gracefully
- **Missing Project**: Sync skipped gracefully  
- **Invalid Stage Mapping**: Sync skipped gracefully
- **No Errors Thrown**: System continues normal operation

## Usage

This feature works automatically in the background with intelligent sync direction:

### For Business Development (Until Active):
1. Create enquiries with Design & Engineering or Fabrication/Prototyping types
2. Move enquiries through pre-sales stages as normal
3. Projects automatically follow enquiry progression
4. Focus on enquiry workflow for business development activities

### For Project Execution (After Active):
1. Once project reaches Active stage, switch focus to project management
2. Move projects through execution stages (Dispatch, Completed, Closed)
3. Enquiries automatically follow project progression
4. Focus on project workflow for delivery activities

### For Early Closure:
1. Move enquiry to Inactive/Closed at any stage
2. Project automatically closes regardless of current stage
3. Immediate closure for business decisions

The synchronization is transparent to users and requires no additional configuration. Users simply work in the appropriate module (enquiry or project) based on the project phase.
