# Enquiry to Project Stage Synchronization

## Overview
This feature automatically synchronizes project stages when enquiry stages change, ensuring that projects always reflect the current status of their originating enquiries.

## Stage Mapping

| Enquiry Stage | Project Stage | Notes |
|---------------|---------------|-------|
| Onboarding | N/A | No project created yet |
| Awaiting Requirements | Awaiting Requirement | Project created at this stage |
| **Pre-Sales** | **Pre Sales** | **Main sync scenario** |
| Quotation Approved | Quotation Approved | Exact match |
| Quotation Sent | Quotation Sent | Exact match |
| Active | Active | Exact match |
| Dispatch | Dispatch | Exact match |
| Completed | Completed | Exact match |
| Inactive/Closed | Closed | Name mapping |

## Workflow

### 1. Project Creation
- **Trigger**: Enquiry moves to "Awaiting Requirements" stage
- **Action**: Project created in "Awaiting Requirement" stage
- **Applies to**: Design & Engineering and Fabrication/Prototyping enquiries only

### 2. Stage Synchronization
- **Trigger**: Any enquiry stage change (after project exists)
- **Action**: Project stage updated to corresponding mapped stage
- **History**: Stage history entry created with current date

### 3. Key Scenario: Pre-Sales Transition
- **Enquiry**: Awaiting Requirements → Pre-Sales
- **Project**: Awaiting Requirement → Pre Sales
- **Result**: Project stage automatically follows enquiry progression

## Technical Implementation

### Enhanced Sync Method
```python
def _sync_project_stage_and_create(self, vals):
    # 1. Check if stage changed
    # 2. Find linked project
    # 3. Map enquiry stage to project stage
    # 4. Update project stage
    # 5. Create stage history entry
```

### Stage History Tracking
- **Model**: `project.stage.history`
- **Fields**: project_id, stage_id, entered_date
- **Purpose**: Complete audit trail of project progression

## Benefits

✅ **Automatic Synchronization**: No manual project stage updates needed  
✅ **Data Consistency**: Projects always reflect enquiry status  
✅ **Audit Trail**: Complete history of stage transitions  
✅ **Error Handling**: Graceful handling of missing stages/projects  
✅ **Selective Sync**: Only applies to project-generating enquiry types  

## Testing Scenarios

1. **Basic Sync**: Awaiting Requirements → Pre-Sales
2. **Full Workflow**: Test all stage transitions
3. **Multiple Enquiry Types**: Design & Engineering, Fabrication/Prototyping
4. **Non-Project Enquiries**: Startup Support (no sync expected)
5. **Stage History**: Verify history entries created

## Error Handling

- **Missing Project Stage**: Sync skipped gracefully
- **Missing Project**: Sync skipped gracefully  
- **Invalid Stage Mapping**: Sync skipped gracefully
- **No Errors Thrown**: System continues normal operation

## Usage

This feature works automatically in the background. Users simply:

1. Create enquiries with Design & Engineering or Fabrication/Prototyping types
2. Move enquiries through stages as normal
3. Projects automatically follow enquiry stage progression
4. View stage history in project form for audit trail

The synchronization is transparent to users and requires no additional configuration.
