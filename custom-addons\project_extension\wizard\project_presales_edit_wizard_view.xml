<odoo>
  <record id="view_project_presales_edit_wizard" model="ir.ui.view">
    <field name="name">project.presales.edit.wizard.form</field>
    <field name="model">project.presales.edit.wizard</field>
    <field name="arch" type="xml">
      <form string="Edit Project Pre-Sales">
      <field name="project_id" invisible="1"/>
        <sheet>
          <group string="Project Deadline">
            <field name="project_deadline" string="Project Deadline"/>
            <group string="Design">
              <field name="design_time" string="Design Time (Hours)"/>
              <field name="design_quantity" string="Design Quantity"/>
              <field name="design_cost" string="Design Cost"/>
            </group>
            <group string="Prototyping">
              <field name="material_cost" string="Material Cost"/>
              <field name="prototyping_quantity" string="Prototyping Quantity"/>
              <field name="prototyping_cost" string="Prototyping Cost"/>
            </group>
          </group>
          <field name="custom_line_item_ids" nolabel="1" groups="project_extension.group_project_manager,project_extension.group_project_operations_admin">
            <tree editable="bottom">
              <field name="name" string="Service name"/>
              <field name="quantity" string="Quantity"/>
              <field name="cost" string="Service cost"/>
              <field name="hsn_code" string="HSN/SAC" default='998130' readonly="1"/>
              <field name="notes" string="Description"/>
            </tree>
          </field>
          <footer>
            <button string="Save" type="object" name="action_save" class="btn btn-primary"/>
            <button string="Cancel" class="btn btn-secondary" special="cancel"/>
          </footer>
        </sheet>
      </form>
    </field>
  </record>

  <record id="action_project_presales_edit_wizard" model="ir.actions.act_window">
    <field name="name">Edit Project Pre-Sales</field>
    <field name="res_model">project.presales.edit.wizard</field>
    <field name="view_mode">form</field>
    <field name="view_id" ref="view_project_presales_edit_wizard"/>
    <field name="target">new</field>
  </record>
</odoo>