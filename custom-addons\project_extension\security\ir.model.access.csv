id,name,model_id:id,group_id:id,perm_read,perm_write,perm_create,perm_unlink
access_project_pr_document_wizard,project.pr.document.wizard,model_project_pr_document_wizard,base.group_user,1,1,1,1

# Project Extension User Groups
access_project_project_user,project.project.user,project.model_project_project,project_extension.group_project_user,1,1,1,0
access_project_project_member,project.project.member,project.model_project_project,project_extension.group_project_member,1,1,0,0
access_project_project_manager,project.project.manager,project.model_project_project,project_extension.group_project_manager,1,1,1,0
access_project_project_extension_manager,project.project.extension.manager,project.model_project_project,project_extension.group_project_extension_manager,1,1,1,1
access_project_project_business_development,project.project.business.development,project.model_project_project,project_extension.group_project_business_development,1,1,1,0
access_project_project_operations_admin,project.project.operations.admin,project.model_project_project,project_extension.group_project_operations_admin,1,1,1,1
access_project_project_accounts,project.project.accounts,project.model_project_project,project_extension.group_project_accounts,1,1,1,0

# Project Task Access
access_project_task_user,project.task.user,project.model_project_task,project_extension.group_project_user,1,1,1,0
access_project_task_member,project.task.member,project.model_project_task,project_extension.group_project_member,1,1,0,0
access_project_task_manager,project.task.manager,project.model_project_task,project_extension.group_project_manager,1,1,1,0
access_project_task_extension_manager,project.task.extension.manager,project.model_project_task,project_extension.group_project_extension_manager,1,1,1,1
access_project_task_business_development,project.task.business.development,project.model_project_task,project_extension.group_project_business_development,1,1,1,0
access_project_task_operations_admin,project.task.operations.admin,project.model_project_task,project_extension.group_project_operations_admin,1,1,1,1
access_project_task_accounts,project.task.accounts,project.model_project_task,project_extension.group_project_accounts,1,1,1,0

# Project Stage Access
access_project_stage_user,project.stage.user,project.model_project_project_stage,project_extension.group_project_user,1,0,0,0
access_project_stage_member,project.stage.member,project.model_project_project_stage,project_extension.group_project_member,1,0,0,0
access_project_stage_manager,project.stage.manager,project.model_project_project_stage,project_extension.group_project_manager,1,1,1,0
access_project_stage_extension_manager,project.stage.extension.manager,project.model_project_project_stage,project_extension.group_project_extension_manager,1,1,1,1
access_project_stage_business_development,project.stage.business.development,project.model_project_project_stage,project_extension.group_project_business_development,1,0,0,0
access_project_stage_operations_admin,project.stage.operations.admin,project.model_project_project_stage,project_extension.group_project_operations_admin,1,1,1,1
access_project_stage_accounts,project.stage.accounts,project.model_project_project_stage,project_extension.group_project_accounts,1,0,0,0
