<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Business Development Group -->
    <record id="group_project_business_development" model="res.groups">
        <field name="name">Business Development</field>
        <field name="category_id" ref="project_extension.module_category_project_extension"/>
        <field name="implied_ids" eval="[(4, ref('project_extension.group_project_user'))]"/>
        <field name="comment">Users in this group can access projects for business development purposes.</field>
    </record>

    <!-- Operations Administrator Group -->
    <record id="group_project_operations_admin" model="res.groups">
        <field name="name">Operations Administrator</field>
        <field name="category_id" ref="project_extension.module_category_project_extension"/>
        <field name="implied_ids" eval="[(4, ref('project_extension.group_project_extension_manager'))]"/>
        <field name="comment">Users in this group can approve project budgets and manage operations.</field>
    </record>

    <!-- Accounts Group -->
    <record id="group_project_accounts" model="res.groups">
        <field name="name">Accounts</field>
        <field name="category_id" ref="project_extension.module_category_project_extension"/>
        <field name="implied_ids" eval="[(4, ref('project_extension.group_project_user'))]"/>
        <field name="comment">Users in this group can manage project financial aspects and approve payments.</field>
    </record>

    <!-- Business Development Access Rules -->
    <record id="rule_project_business_development" model="ir.rule">
        <field name="name">Project Access for Business Development</field>
        <field name="model_id" ref="project.model_project_project"/>
        <field name="domain_force">[(1, '=', 1)]</field>
        <field name="groups" eval="[(4, ref('group_project_business_development'))]"/>
        <field name="perm_read" eval="True"/>
        <field name="perm_write" eval="True"/>
        <field name="perm_create" eval="True"/>
        <field name="perm_unlink" eval="False"/>
    </record>

    <!-- Operations Administrator Access Rules -->
    <record id="rule_project_operations_admin" model="ir.rule">
        <field name="name">Project Operations Administrator Access</field>
        <field name="model_id" ref="project.model_project_project"/>
        <field name="domain_force">[(1, '=', 1)]</field>
        <field name="groups" eval="[(4, ref('group_project_operations_admin'))]"/>
        <field name="perm_read" eval="True"/>
        <field name="perm_write" eval="True"/>
        <field name="perm_create" eval="True"/>
        <field name="perm_unlink" eval="True"/>
    </record>

    <!-- Accounts Access Rules -->
    <record id="rule_project_accounts" model="ir.rule">
        <field name="name">Project Accounts Access</field>
        <field name="model_id" ref="project.model_project_project"/>
        <field name="domain_force">[(1, '=', 1)]</field>
        <field name="groups" eval="[(4, ref('group_project_accounts'))]"/>
        <field name="perm_read" eval="True"/>
        <field name="perm_write" eval="True"/>
        <field name="perm_create" eval="True"/>
        <field name="perm_unlink" eval="False"/>
    </record>
</odoo>
