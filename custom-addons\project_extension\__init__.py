from . import models
from . import wizard
from odoo import SUPERUSER_ID

def remove_default_project_stages(cr, registry):
    from odoo.api import Environment
    env = Environment(cr, SUPERUSER_ID, {})
    # List your custom stage names
    custom_stage_names = [
        'Pre Sales', 'Awaiting Requirement', 'Quotation Approved', 'Quotation Sent', 'Active', 'Dispatch', 'Completed', 'Closed'
    ]
    stages = env['project.project.stage'].search([])
    for stage in stages:
        if stage.name not in custom_stage_names:
            stage.unlink()