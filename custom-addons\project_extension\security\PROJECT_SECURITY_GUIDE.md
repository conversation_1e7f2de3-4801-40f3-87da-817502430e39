# Project Extension Security Groups Guide

## Overview
This document outlines the security groups and permissions structure for the Project Extension module, designed to mirror the enquiry module's security structure.

## Security Groups Structure

### 1. **User** (`group_project_user`)
- **Description**: Basic project access for all users
- **Permissions**: 
  - Read, Write, Create projects (no delete)
  - Read, Write, Create tasks (no delete)
  - Read-only access to project stages
- **Use Case**: General employees who need basic project access

### 2. **Project Member** (`group_project_member`)
- **Description**: Users with read-only access to all projects
- **Permissions**:
  - Read-only access to all projects (no write/create/delete)
  - Read-only access to all tasks (no write/create/delete)
  - Read-only access to project stages
- **Use Case**: Team members who need to view all projects but cannot make changes
- **Access Rule**: Can see all projects but cannot modify anything

### 3. **Project Manager** (`group_project_manager`)
- **Description**: Users who manage projects and teams
- **Permissions**:
  - Read, Write, Create projects (no delete)
  - Read, Write, <PERSON>reate tasks (no delete)
  - Read, Write, Create project stages (no delete)
- **Use Case**: Project managers who oversee multiple projects and assign team members

### 4. **Business Development** (`group_project_business_development`)
- **Description**: Users handling business development aspects
- **Permissions**:
  - Read, Write, Create projects (no delete)
  - Read, Write, Create tasks (no delete)
  - Read-only access to project stages
- **Use Case**: Business development team members working on project proposals and client relations

### 5. **Operations Administrator** (`group_project_operations_admin`)
- **Description**: Users who manage operations and can approve budgets
- **Permissions**:
  - Full access: Read, Write, Create, Delete projects
  - Full access: Read, Write, Create, Delete tasks
  - Full access: Read, Write, Create, Delete project stages
- **Use Case**: Operations managers who need full control over project operations
- **Inherits**: Manager permissions

### 6. **Accounts** (`group_project_accounts`)
- **Description**: Users managing financial aspects of projects
- **Permissions**:
  - Read, Write, Create projects (no delete)
  - Read, Write, Create tasks (no delete)
  - Read-only access to project stages
- **Use Case**: Accounts team members handling project budgets and financial tracking

### 7. **Manager** (`group_project_extension_manager`)
- **Description**: Highest level access for project management
- **Permissions**:
  - Full access: Read, Write, Create, Delete projects
  - Full access: Read, Write, Create, Delete tasks
  - Full access: Read, Write, Create, Delete project stages
- **Use Case**: Senior managers and administrators
- **Default Users**: Root and Admin users

## Group Hierarchy

```
Manager (Full Access)
├── Operations Administrator (Full Access)
├── Project Manager (Manage Projects & Teams)
├── Business Development (BD Operations)
├── Accounts (Financial Management)
├── Project Member (Assigned Projects Only)
└── User (Basic Access)
```

## Permission Matrix

| Group | Projects | Tasks | Stages | Delete Rights | Special Access |
|-------|----------|-------|--------|---------------|----------------|
| User | RWC | RWC | R | No | All projects |
| Project Member | R | R | R | No | All projects (READ-ONLY) |
| Project Manager | RWC | RWC | RWC | No | All projects |
| Business Development | RWC | RWC | R | No | All projects |
| Accounts | RWC | RWC | R | No | All projects |
| Operations Administrator | RWCD | RWCD | RWCD | Yes | All projects |
| Manager | RWCD | RWCD | RWCD | Yes | All projects |

**Legend**: R=Read, W=Write, C=Create, D=Delete

## Implementation Files

1. **`security/project_security.xml`**: Core security groups and basic rules
2. **`security/project_additional_groups.xml`**: Additional specialized groups
3. **`security/ir.model.access.csv`**: Detailed model access permissions

## Usage Instructions

### For Administrators:
1. Assign users to appropriate groups based on their roles
2. Project Members should be assigned to specific projects via the project form
3. Operations Administrators have full control and should be assigned carefully

### For Project Managers:
1. Can create and manage projects
2. Can assign team members to projects
3. Can manage project stages and tasks

### For Project Members:
1. Can view all projects in the system
2. Have read-only access - cannot make any changes
3. Cannot create new projects or delete existing ones
4. Cannot edit tasks or project information

## Security Rules Summary

- **Global Access**: All groups can access projects based on their permissions
- **Project Member Access**: Project members can see all projects but with read-only access
- **Manager Override**: Managers and Operations Administrators have unrestricted access
- **Delete Protection**: Only Managers and Operations Administrators can delete records
