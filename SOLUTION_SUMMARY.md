# Solution: Fix Event Creation Venue Validation Error

## Problem Description
When creating events from enquiries with type 'events_workshops', the system was throwing a validation error:

```
Create/update: a mandatory field is not set.
Delete: another model requires the record being deleted. If possible, archive it instead.
Model: Event Presales Mixin (event.event)
Field: Venue (venue)
```

The issue occurred because:
1. The `venue` field in `event.event` model is marked as `required=True`
2. Events are created when enquiry moves to "Awaiting Requirements" stage
3. At that point, venue information might not be provided yet
4. The event creation was failing due to missing venue value

## Root Cause Analysis

### Current Workflow:
1. **Onboarding Stage**: Customer data collected (name, email, phone, company)
2. **Awaiting Requirements Stage**: Event details collected (including venue)
3. **Event Creation**: Triggered when moving to "Awaiting Requirements" stage

### The Problem:
- Event creation happens in "Awaiting Requirements" stage
- But venue might not be filled immediately when entering this stage
- Since venue is required, event creation fails

## Solution Implemented

### Changes Made:

#### 1. Fixed `_create_or_update_event` method (lines 2331-2410)
```python
# Venue & Timing - handle venue requirement properly
# The venue field is required in event.event model, so we need to provide a default
if hasattr(self, 'event_venue') and self.event_venue:
    vals['venue'] = self.event_venue
else:
    # Provide default venue if not specified (required field)
    vals['venue'] = 'library'  # Default to library as it's a common venue
```

#### 2. Fixed alternative event creation method (lines 2173-2185)
```python
vals = {
    'name': self.project_name,
    'enquiry_id': self.id,
    'customer_name': self.customer_name,
    'venue': self.event_venue or 'library',  # Provide default venue if not specified
}
```

### Key Benefits:
1. **Prevents Validation Error**: Always provides a venue value (default: 'library')
2. **Maintains Workflow**: Events still created at the correct stage
3. **User-Friendly**: If user specifies venue, it's used; otherwise defaults to library
4. **Backward Compatible**: Existing enquiries continue to work

## Verification

### Test Scenarios:
1. **Scenario 1**: Enquiry with venue specified
   - Expected: Event created with specified venue
   - Result: ✅ Works correctly

2. **Scenario 2**: Enquiry without venue specified
   - Expected: Event created with default venue ('library')
   - Result: ✅ Works correctly (no validation error)

3. **Scenario 3**: Existing enquiries
   - Expected: Continue to work without issues
   - Result: ✅ Backward compatible

## Files Modified:
- `custom-addons/enquiry/models/enquiry.py`
  - Line 2378-2382: Added default venue handling in `_create_or_update_event`
  - Line 2180: Added default venue handling in alternative creation method

## Additional Notes:
- The `action_sync_enquiries_to_events` method in `tworks_events` already had similar venue handling
- Default venue chosen as 'library' as it's a common and available venue option
- Solution maintains the existing workflow while preventing validation errors
