# Enquiry "Sync Presales Data to Project" Button

## Overview
Added a new sync button in the enquiry Pre-Sales tab that allows users to manually sync presales data from enquiry to linked project, providing bidirectional sync capability.

## Implementation

### Button Location
- **Module**: Enquiry
- **View**: Enquiry Form → Pre-Sales Tab → Bottom Section
- **Position**: Left side, next to "Get JSON" button

### Button Properties
- **Name**: "Sync Presales Data to Project"
- **Type**: Object button
- **Method**: `sync_presales_to_project`
- **Style**: `btn btn-info` (blue color)
- **Help Text**: "Sync presales data from enquiry to linked project"

## Functionality

### Data Synced (Enquiry → Project)
- Design cost and time
- Material cost
- Customer name and company name
- Prototyping cost and quantity
- Design quantity
- Project deadline
- PR documents/attachments
- Custom line items (complete replacement)
- Last presales updated timestamp

### Enhanced Sync Method
```python
def sync_presales_to_project(self):
    # 1. Find linked project
    # 2. Update all presales fields
    # 3. Replace custom line items
    # 4. Recompute project costs
    # 5. Update timestamp
    # 6. Show user notification
```

### User Feedback
- **Success**: "Presales data synced to X project(s) successfully"
- **Warning**: "No linked projects found to sync"
- **Notifications**: Toast notifications with appropriate styling

## Bidirectional Sync Capability

### Enquiry → Project (NEW)
- **Location**: Enquiry Pre-Sales tab
- **Button**: "Sync Presales Data to Project"
- **Purpose**: Update project with enquiry changes

### Project → Enquiry (Existing)
- **Location**: Project form
- **Method**: Automatic sync in write method
- **Purpose**: Update enquiry with project changes

## Benefits

✅ **Manual Control**: Users can trigger sync when needed  
✅ **Bidirectional**: Complete sync capability in both directions  
✅ **User Feedback**: Clear notifications about sync results  
✅ **Data Integrity**: Complete replacement of custom line items  
✅ **Audit Trail**: Proper logging and timestamp updates  
✅ **Error Handling**: Graceful handling when no project exists  

## Usage

### For Enquiries with Linked Projects
1. Open enquiry in form view
2. Navigate to Pre-Sales tab
3. Modify presales data as needed
4. Click "Sync Presales Data to Project" button
5. Receive success notification
6. Project data is updated immediately

### For Enquiries without Linked Projects
1. Click sync button
2. Receive warning notification
3. No errors or system issues

## Testing Scenarios

1. **Button Visibility**: Verify button appears in Pre-Sales tab
2. **Successful Sync**: Test with enquiry that has linked project
3. **No Project Warning**: Test with enquiry without linked project
4. **Custom Line Items**: Verify line items sync correctly
5. **Bidirectional Sync**: Test both enquiry→project and project→enquiry

## Security & Permissions

- Uses existing enquiry access permissions
- No additional security restrictions
- Respects project access permissions during sync
- Context flag prevents infinite sync loops

## Next Steps

1. Upgrade enquiry module to apply changes
2. Test button functionality with different scenarios
3. Train users on new sync capability
4. Monitor sync operations through logs

This enhancement provides users with complete control over presales data synchronization between enquiries and projects.
