<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Security Groups -->
    <record id="module_category_project_extension" model="ir.module.category">
        <field name="name">Project Extension</field>
        <field name="description">Helps you manage your projects with enhanced permissions</field>
        <field name="sequence">21</field>
    </record>

    <!-- Base User Group -->
    <record id="group_project_user" model="res.groups">
        <field name="name">User</field>
        <field name="category_id" ref="module_category_project_extension"/>
        <field name="implied_ids" eval="[(4, ref('base.group_user'))]"/>
        <field name="comment">Basic project access for all users</field>
    </record>

    <!-- Project Manager Group -->
    <record id="group_project_manager" model="res.groups">
        <field name="name">Project Manager</field>
        <field name="category_id" ref="module_category_project_extension"/>
        <field name="implied_ids" eval="[(4, ref('group_project_user'))]"/>
        <field name="comment">Users in this group can manage projects, edit cost estimates, and assign team members.</field>
    </record>

    <!-- Manager Group (highest level) -->
    <record id="group_project_extension_manager" model="res.groups">
        <field name="name">Manager</field>
        <field name="category_id" ref="module_category_project_extension"/>
        <field name="implied_ids" eval="[(4, ref('group_project_manager'))]"/>
        <field name="users" eval="[(4, ref('base.user_root')), (4, ref('base.user_admin'))]"/>
        <field name="comment">Full access to all project management features</field>
    </record>

    <!-- Project Members Group -->
    <record id="group_project_member" model="res.groups">
        <field name="name">Project Member</field>
        <field name="category_id" ref="module_category_project_extension"/>
        <field name="implied_ids" eval="[(4, ref('group_project_user'))]"/>
        <field name="comment">Users in this group can view and update projects they are assigned to.</field>
    </record>

    <!-- Access Rules -->
    <record id="project_rule_all" model="ir.rule">
        <field name="name">Project access rule</field>
        <field name="model_id" ref="project.model_project_project"/>
        <field name="global" eval="True"/>
        <field name="domain_force">[(1, '=', 1)]</field>
    </record>

    <!-- Project Manager Rules -->
    <record id="rule_project_manager_access" model="ir.rule">
        <field name="name">Project Manager Full Access</field>
        <field name="model_id" ref="project.model_project_project"/>
        <field name="domain_force">[(1, '=', 1)]</field>
        <field name="groups" eval="[(4, ref('group_project_manager'))]"/>
        <field name="perm_read" eval="True"/>
        <field name="perm_write" eval="True"/>
        <field name="perm_create" eval="True"/>
        <field name="perm_unlink" eval="False"/>
    </record>

    <!-- Project Member Rules - can only access assigned projects -->
    <record id="rule_project_member_access" model="ir.rule">
        <field name="name">Project Member Access to Assigned Projects</field>
        <field name="model_id" ref="project.model_project_project"/>
        <field name="domain_force">['|', ('user_id', '=', user.id), ('favorite_user_ids', 'in', user.id)]</field>
        <field name="groups" eval="[(4, ref('group_project_member'))]"/>
        <field name="perm_read" eval="True"/>
        <field name="perm_write" eval="True"/>
        <field name="perm_create" eval="False"/>
        <field name="perm_unlink" eval="False"/>
    </record>
</odoo>
